{% extends 'base.html' %}
{% block title %}New Note{% endblock %}
{% block content %}
    <div class="flex items-center justify-between">
        <h1 class="font-semibold text-2xl text-gray-800 transition-colors duration-300">
            Create a new notes
        </h1>
    </div>

    <div class="flex items-center w-full max-w-lg px-6 mx-auto">
        <div class="flex-1">
            <div class="mt-8">
                <form method="post">
                    <div>
                        <label for="title" class="block mb-2 text-sm text-gray-500 ">Title</label>
                        <input type="text" name="title" id="title" placeholder="My Amazing and Awesome Note" required
                               class="block w-full px-4 py-2 mt-2 text-black placeholder-gray-400  border border-gray-200 rounded-lg  focus:border-blue-400  focus:ring-blue-400 focus:outline-none focus:ring focus:ring-opacity-40"/>
                    </div>

                    <div class="mt-6">
                        <label for="content" class="text-sm text-gray-500 ">Content</label>

                        <textarea type="text" name="content" id="content" placeholder="Write your note here..." required
                                  class="min-h-40  block w-full px-4 py-2 mt-2 text-black placeholder-gray-400  border border-gray-200 rounded-lg focus:border-blue-400  focus:ring-blue-400 focus:outline-none focus:ring focus:ring-opacity-40"></textarea>
                    </div>

                    <div class="mt-6">
                        <input type="hidden" name="image_path" required value="{{ image_path }}">
                        <button type="submit"
                                class="w-full px-4 py-2 tracking-wide text-white transition-colors duration-300 transform bg-blue-600 rounded-md hover:bg-blue-500 focus:outline-none focus:ring focus:ring-blue-300 focus:ring-opacity-80">
                            Save Note
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
{% endblock %}
