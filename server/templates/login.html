{% extends 'base.html' %}
{% block title %}Login{% endblock %}
{% block content %}
    <div class="flex items-center justify-center">
        <h1 class="font-semibold text-2xl text-gray-800 transition-colors duration-300">
            Login
        </h1>
    </div>

    <div class="flex items-center w-full max-w-md px-6 mx-auto lg:w-2/6">
        <div class="flex-1">
            <div class="mt-8">
                <form method="post">
                    <div>
                        <label for="username" class="block mb-2 text-sm text-gray-500 ">Username</label>
                        <input type="text" name="username" id="username" placeholder="hackerman" required
                               class="block w-full px-4 py-2 mt-2 text-black placeholder-gray-400  border border-gray-200 rounded-lg  focus:border-blue-400  focus:ring-blue-400 focus:outline-none focus:ring focus:ring-opacity-40"/>
                    </div>

                    <div class="mt-6">
                        <div class="flex justify-between mb-2">
                            <label for="password" class="text-sm text-gray-500 ">Password</label>
                            <span class="text-sm text-gray-500">(optional)</span>
                        </div>

                        <input type="password" name="password" id="password" placeholder="Your Password"
                               class="block w-full px-4 py-2 mt-2 text-black placeholder-gray-400  border border-gray-200 rounded-lg focus:border-blue-400  focus:ring-blue-400 focus:outline-none focus:ring focus:ring-opacity-40"/>
                    </div>

                    <div class="mt-6">
                        <button type="submit"
                                class="w-full px-4 py-2 tracking-wide text-white transition-colors duration-300 transform bg-blue-600 rounded-md hover:bg-blue-500 focus:outline-none focus:ring focus:ring-blue-300 focus:ring-opacity-80">
                            Sign in
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

{% endblock %}